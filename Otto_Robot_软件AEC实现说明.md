# Otto Robot 软件AEC实现说明

## 概述

本文档详细说明了Otto Robot软件声学回声消除(AEC)的实现方案。该方案基于小智AI开发板的软件AEC技术，通过虚拟参考信号实现在没有硬件回采通道情况下的回声消除功能。

## 技术原理

### 1. 软件AEC工作原理

软件AEC通过以下方式工作：
- **虚拟参考信号**: 将扬声器播放的音频数据缓存，作为AEC算法的参考信号
- **双通道处理**: 麦克风采集的音频与参考信号组成双通道数据输入AEC算法
- **实时处理**: ESP-SR组件的AFE模块实时处理音频，消除回声

### 2. 关键技术要点

#### 数据同步
- 扬声器播放缓存与麦克风采集数据需要时间同步
- 允许±60ms的同步误差，超过100ms会清空缓存

#### 采样率统一
- 麦克风和扬声器都使用16kHz采样率
- 确保AEC算法输入的两路信号频率一致

#### 数据精度优化
- 麦克风数据右移8位而非12位，保持更好的精度
- 避免因精度损失影响AEC效果

## 代码实现

### 1. 配置选项 (main/Kconfig.projbuild)

```kconfig
config USE_REALTIME_CHAT
    bool "启用可语音打断的实时对话模式（需要 AEC 支持）"
    default n
    depends on USE_AUDIO_PROCESSOR && (... || BOARD_TYPE_OTTO_ROBOT)
    help
        需要 ESP32 S3 与 AEC 开启，因为性能不够，不建议和微信聊天界面风格同时开启
```

### 2. 音频采样率配置 (main/boards/otto-robot/config.h)

```c
#define AUDIO_INPUT_SAMPLE_RATE 16000
#define AUDIO_OUTPUT_SAMPLE_RATE 16000  // 修改为16kHz以支持软件AEC
```

### 3. AEC模式配置 (main/audio_processing/afe_audio_processor.cc)

```cpp
afe_config->aec_mode = AEC_MODE_VOIP_HIGH_PERF;  // 高性能AEC模式
```

### 4. 虚拟参考信号实现 (main/audio_codecs/no_audio_codec.cc)

#### 构造函数中的初始化
```cpp
#ifdef CONFIG_USE_DEVICE_AEC
    // 启用虚拟参考信号用于软件AEC
    input_reference_ = true;
    input_channels_ = 2;  // 麦克风 + 参考信号
    
    // 初始化播放缓存相关变量
    time_us_write_ = 0;
    time_us_read_ = 0;
    slice_index_ = 0;
#endif
```

#### Write方法中的播放缓存
```cpp
#ifdef CONFIG_USE_DEVICE_AEC
    // 为软件AEC缓存播放数据
    {
        std::unique_lock<std::mutex> lock(mutex_);
        if (output_buffer_.size() < play_size*10) {
            output_buffer_.resize(play_size*10, 0);
            slice_index_ = 0;
        }
        
        for (int i = 0; i < samples; i++) {
            output_buffer_[slice_index_] = data[i];
            slice_index_++;
            if(slice_index_ >= play_size*10) slice_index_ = 0;
        }
    }
#endif
```

#### Read方法中的双通道数据生成
```cpp
#ifdef CONFIG_USE_DEVICE_AEC
    if (input_reference_) {
        // 处理双通道数据：麦克风+参考信号
        int mic_samples = samples / 2;
        // ... 读取麦克风数据 ...
        
        for (int i = 0; i < actual_mic_samples; i++) {
            // 麦克风数据处理：右移8位保持精度
            int32_t value = bit32_buffer[i] >> 8;
            dest[i*2] = (int16_t)value;  // 麦克风通道
            
            // 参考信号通道：从播放缓存获取
            if (output_buffer_.size() > 0 && i_index < output_buffer_.size()) {
                dest[i*2 + 1] = output_buffer_[i_index];
                i_index++;
                if(i_index >= play_size*10) i_index = 0;
            } else {
                dest[i*2 + 1] = 0;  // 无播放数据时置零
            }
        }
        return actual_mic_samples*2;  // 返回双通道总样本数
    }
#endif
```

## 使用方法

### 1. 编译配置

使用menuconfig启用相关选项：
```bash
idf.py menuconfig
```

在菜单中选择：
- `Xiaozhi Assistant` -> `Board Type` -> `ottoRobot`
- `Xiaozhi Assistant` -> `Enable Device-Side AEC` -> `Y`
- `Xiaozhi Assistant` -> `启用可语音打断的实时对话模式` -> `Y`

### 2. 编译和烧录

```bash
idf.py build
idf.py flash monitor
```

### 3. 测试验证

运行测试程序验证AEC功能：
```bash
# 编译测试程序
g++ -o test_aec test_software_aec.cpp -I./main -I./components

# 或者直接在设备上运行内置测试
```

## 性能优化建议

### 1. 内存优化
- 播放缓存大小设置为1.6秒(play_size*10)
- 使用循环缓存避免内存碎片

### 2. 实时性优化
- AEC处理运行在核心1，优先级为1
- 使用PSRAM存储AEC模型和缓存

### 3. 音质优化
- 麦克风数据右移8位而非12位
- 使用高性能AEC模式(AEC_MODE_VOIP_HIGH_PERF)

## 故障排除

### 1. 常见问题

**问题**: AEC效果不佳
**解决**: 
- 检查采样率是否统一为16kHz
- 验证播放缓存是否正常工作
- 确认麦克风数据精度处理正确

**问题**: 实时对话无法打断
**解决**:
- 确认CONFIG_USE_DEVICE_AEC已启用
- 检查CONFIG_USE_REALTIME_CHAT配置
- 验证音频处理器初始化正常

**问题**: 性能问题/卡顿
**解决**:
- 降低AEC复杂度设置
- 检查PSRAM使用情况
- 优化任务优先级配置

### 2. 调试方法

启用音频调试功能：
```c
#define CONFIG_USE_AUDIO_DEBUGGER 1
```

查看AEC处理日志：
```c
esp_log_level_set("AFE_AUDIO_PROCESSOR", ESP_LOG_DEBUG);
esp_log_level_set("NO_AUDIO_CODEC", ESP_LOG_DEBUG);
```

## 总结

Otto Robot的软件AEC实现通过虚拟参考信号技术，成功在没有硬件回采的情况下实现了声学回声消除功能。该方案具有以下优势：

1. **无需硬件改动**: 纯软件实现，适用于现有硬件
2. **实时性好**: 支持语音打断的实时对话
3. **效果稳定**: 基于成熟的ESP-SR AEC算法
4. **资源占用合理**: 优化的内存和CPU使用

通过正确的配置和优化，可以获得良好的AEC效果，显著提升语音交互体验。
