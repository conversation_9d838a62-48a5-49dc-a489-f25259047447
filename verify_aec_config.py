#!/usr/bin/env python3
"""
Otto Robot 软件AEC配置验证脚本
验证所有必要的配置是否正确设置
"""

import os
import re
import sys
from pathlib import Path

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (文件不存在)")
        return False

def check_config_option(file_path, pattern, description):
    """检查配置文件中的选项"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if re.search(pattern, content, re.MULTILINE):
                print(f"✅ {description}")
                return True
            else:
                print(f"❌ {description}")
                return False
    except Exception as e:
        print(f"❌ {description}: 读取文件失败 - {e}")
        return False

def check_code_implementation(file_path, pattern, description):
    """检查代码实现"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if re.search(pattern, content, re.DOTALL):
                print(f"✅ {description}")
                return True
            else:
                print(f"❌ {description}")
                return False
    except Exception as e:
        print(f"❌ {description}: 读取文件失败 - {e}")
        return False

def main():
    print("=" * 60)
    print("Otto Robot 软件AEC配置验证")
    print("=" * 60)
    
    all_checks_passed = True
    
    # 1. 检查关键文件是否存在
    print("\n1. 检查关键文件...")
    files_to_check = [
        ("main/Kconfig.projbuild", "Kconfig配置文件"),
        ("main/boards/otto-robot/config.h", "Otto Robot配置文件"),
        ("main/audio_codecs/no_audio_codec.cc", "音频编解码器实现"),
        ("main/audio_codecs/no_audio_codec.h", "音频编解码器头文件"),
        ("main/audio_processing/afe_audio_processor.cc", "AFE音频处理器"),
    ]
    
    for file_path, description in files_to_check:
        if not check_file_exists(file_path, description):
            all_checks_passed = False
    
    # 2. 检查Kconfig配置
    print("\n2. 检查Kconfig配置...")
    kconfig_checks = [
        (r"config USE_REALTIME_CHAT", "实时对话配置项存在"),
        (r"BOARD_TYPE_OTTO_ROBOT", "Otto Robot板型配置存在"),
        (r"depends on.*BOARD_TYPE_OTTO_ROBOT", "Otto Robot依赖配置正确"),
    ]
    
    for pattern, description in kconfig_checks:
        if not check_config_option("main/Kconfig.projbuild", pattern, description):
            all_checks_passed = False
    
    # 3. 检查Otto Robot配置
    print("\n3. 检查Otto Robot配置...")
    otto_config_checks = [
        (r"#define AUDIO_INPUT_SAMPLE_RATE 16000", "输入采样率设置为16kHz"),
        (r"#define AUDIO_OUTPUT_SAMPLE_RATE 16000", "输出采样率设置为16kHz"),
        (r"#define AUDIO_I2S_METHOD_SIMPLEX", "I2S单工模式配置"),
    ]
    
    for pattern, description in otto_config_checks:
        if not check_config_option("main/boards/otto-robot/config.h", pattern, description):
            all_checks_passed = False
    
    # 4. 检查AEC模式配置
    print("\n4. 检查AEC模式配置...")
    if check_file_exists("main/audio_processing/afe_audio_processor.cc", "AFE音频处理器"):
        aec_mode_checks = [
            (r"afe_config->aec_mode = AEC_MODE_VOIP_HIGH_PERF", "AEC高性能模式配置"),
            (r"#ifdef CONFIG_USE_DEVICE_AEC", "设备端AEC条件编译"),
        ]
        
        for pattern, description in aec_mode_checks:
            if not check_code_implementation("main/audio_processing/afe_audio_processor.cc", pattern, description):
                all_checks_passed = False
    
    # 5. 检查虚拟参考信号实现
    print("\n5. 检查虚拟参考信号实现...")
    if check_file_exists("main/audio_codecs/no_audio_codec.h", "音频编解码器头文件"):
        header_checks = [
            (r"std::vector<int16_t> output_buffer_", "播放缓存变量定义"),
            (r"std::mutex mutex_", "线程安全锁定义"),
            (r"size_t slice_index_", "缓存索引变量定义"),
        ]
        
        for pattern, description in header_checks:
            if not check_code_implementation("main/audio_codecs/no_audio_codec.h", pattern, description):
                all_checks_passed = False
    
    if check_file_exists("main/audio_codecs/no_audio_codec.cc", "音频编解码器实现"):
        implementation_checks = [
            (r"input_reference_ = true", "参考信号启用"),
            (r"input_channels_ = 2", "双通道配置"),
            (r"output_buffer_\[slice_index_\] = data\[i\]", "播放数据缓存"),
            (r"dest\[i\*2\] = .*temp", "麦克风通道数据"),
            (r"dest\[i\*2 \+ 1\] = output_buffer_", "参考信号通道数据"),
            (r"bit32_buffer\[i\] >> 8", "8位右移精度优化"),
        ]
        
        for pattern, description in implementation_checks:
            if not check_code_implementation("main/audio_codecs/no_audio_codec.cc", pattern, description):
                all_checks_passed = False
    
    # 6. 检查编译配置
    print("\n6. 检查编译配置...")
    if os.path.exists("build/config/sdkconfig"):
        build_checks = [
            (r"CONFIG_USE_DEVICE_AEC=y", "设备端AEC已启用"),
            (r"CONFIG_BOARD_TYPE_OTTO_ROBOT=y", "Otto Robot板型已选择"),
        ]
        
        for pattern, description in build_checks:
            if not check_config_option("build/config/sdkconfig", pattern, description):
                print(f"⚠️  {description} (需要重新配置)")
    else:
        print("⚠️  未找到编译配置文件，请先运行 idf.py build")
    
    # 总结
    print("\n" + "=" * 60)
    if all_checks_passed:
        print("🎉 所有配置检查通过！Otto Robot软件AEC配置正确。")
        print("\n下一步操作:")
        print("1. 运行 idf.py menuconfig 确认配置")
        print("2. 运行 idf.py build 编译项目")
        print("3. 运行 idf.py flash monitor 烧录并测试")
    else:
        print("⚠️  发现配置问题，请检查上述失败项目。")
        print("\n建议操作:")
        print("1. 检查文件是否存在")
        print("2. 确认代码修改是否正确")
        print("3. 重新运行配置和编译")
    
    print("=" * 60)
    
    return 0 if all_checks_passed else 1

if __name__ == "__main__":
    sys.exit(main())
