#!/usr/bin/env python3
"""
验证Otto机器人四肢功能屏蔽配置的脚本
"""

import os
import re

def check_config_file():
    """检查config.h文件中的配置"""
    config_path = "main/boards/otto-robot/config.h"
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查ENABLE_LIMB_ACTIONS设置
    enable_match = re.search(r'#define\s+ENABLE_LIMB_ACTIONS\s+(\d+)', content)
    if enable_match:
        value = int(enable_match.group(1))
        if value == 0:
            print("✅ ENABLE_LIMB_ACTIONS 正确设置为 0")
        else:
            print(f"❌ ENABLE_LIMB_ACTIONS 设置为 {value}，应该为 0")
            return False
    else:
        print("❌ 未找到 ENABLE_LIMB_ACTIONS 定义")
        return False
    
    # 检查四肢引脚设置
    limb_pins = [
        'RIGHT_LEG_PIN', 'RIGHT_FOOT_PIN', 'LEFT_LEG_PIN', 
        'LEFT_FOOT_PIN', 'LEFT_HAND_PIN', 'RIGHT_HAND_PIN'
    ]
    
    all_disabled = True
    for pin in limb_pins:
        pattern = rf'#define\s+{pin}\s+(-?\d+)'
        match = re.search(pattern, content)
        if match:
            value = int(match.group(1))
            if value == -1:
                print(f"✅ {pin} 正确设置为 -1 (已禁用)")
            else:
                print(f"❌ {pin} 设置为 {value}，应该为 -1")
                all_disabled = False
        else:
            print(f"❌ 未找到 {pin} 定义")
            all_disabled = False
    
    return all_disabled

def check_head_pins():
    """检查头部功能引脚是否正确配置"""
    config_path = "main/boards/otto-robot/config.h"
    
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查头部功能引脚
    head_pins = {
        'AUDIO_I2S_MIC_GPIO_WS': 'GPIO_NUM_4',
        'AUDIO_I2S_MIC_GPIO_SCK': 'GPIO_NUM_5',
        'AUDIO_I2S_MIC_GPIO_DIN': 'GPIO_NUM_6',
        'AUDIO_I2S_SPK_GPIO_DOUT': 'GPIO_NUM_7',
        'DISPLAY_BACKLIGHT_PIN': 'GPIO_NUM_3',
        'DISPLAY_MOSI_PIN': 'GPIO_NUM_10',
        'BOOT_BUTTON_GPIO': 'GPIO_NUM_0'
    }
    
    all_correct = True
    for pin_name, expected_value in head_pins.items():
        pattern = rf'#define\s+{pin_name}\s+(\S+)'
        match = re.search(pattern, content)
        if match:
            actual_value = match.group(1)
            if actual_value == expected_value:
                print(f"✅ {pin_name} 正确设置为 {expected_value}")
            else:
                print(f"⚠️  {pin_name} 设置为 {actual_value}，期望 {expected_value}")
        else:
            print(f"❌ 未找到 {pin_name} 定义")
            all_correct = False
    
    return all_correct

def check_controller_file():
    """检查otto_controller.cc文件中的条件编译"""
    controller_path = "main/boards/otto-robot/otto_controller.cc"
    
    if not os.path.exists(controller_path):
        print(f"❌ 控制器文件不存在: {controller_path}")
        return False
    
    with open(controller_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键的条件编译块
    checks = [
        ('#if ENABLE_LIMB_ACTIONS', '条件编译保护'),
        ('四肢功能已屏蔽', '屏蔽日志信息'),
        ('仅保留头部功能', '头部功能保留说明')
    ]
    
    all_found = True
    for pattern, description in checks:
        if pattern in content:
            print(f"✅ 找到 {description}")
        else:
            print(f"❌ 未找到 {description}: {pattern}")
            all_found = False
    
    return all_found

def check_movements_file():
    """检查otto_movements.cc文件的条件编译"""
    movements_path = "main/boards/otto-robot/otto_movements.cc"
    
    if not os.path.exists(movements_path):
        print(f"❌ 动作文件不存在: {movements_path}")
        return False
    
    with open(movements_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查文件开头和结尾的条件编译
    if content.startswith('#include "config.h"\n\n#if ENABLE_LIMB_ACTIONS'):
        print("✅ otto_movements.cc 正确使用条件编译保护")
    else:
        print("❌ otto_movements.cc 条件编译保护不正确")
        return False
    
    if content.strip().endswith('#endif  // ENABLE_LIMB_ACTIONS'):
        print("✅ otto_movements.cc 正确结束条件编译")
    else:
        print("❌ otto_movements.cc 条件编译结束不正确")
        return False
    
    return True

def main():
    """主验证函数"""
    print("🔍 开始验证Otto机器人四肢功能屏蔽配置...")
    print("=" * 50)
    
    results = []
    
    print("\n📋 检查配置文件 (config.h):")
    results.append(check_config_file())
    
    print("\n🎯 检查头部功能引脚:")
    results.append(check_head_pins())
    
    print("\n🎮 检查控制器文件 (otto_controller.cc):")
    results.append(check_controller_file())
    
    print("\n🤖 检查动作文件 (otto_movements.cc):")
    results.append(check_movements_file())
    
    print("\n" + "=" * 50)
    
    if all(results):
        print("🎉 所有检查通过！四肢功能已完全屏蔽，仅保留头部功能。")
        print("\n📝 屏蔽的功能:")
        print("   - 所有四肢动作 (行走、转身、跳跃等)")
        print("   - 手部动作 (举手、挥手等)")
        print("   - 舵机控制和微调")
        print("\n✨ 保留的头部功能:")
        print("   - 表情显示 (GIF动画)")
        print("   - 音频输入/输出")
        print("   - 显示屏控制")
        print("   - 摄像头功能")
        print("   - WiFi和系统功能")
        return True
    else:
        print("❌ 部分检查失败，请检查配置！")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
