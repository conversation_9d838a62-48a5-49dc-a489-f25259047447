# Otto-Robot 唤醒词问题诊断指南

## 🔍 问题分析

### 发现的问题
1. **软件AEC配置影响**: 启用软件AEC后，音频输入格式从单通道变为双通道
2. **数据大小不匹配**: AFE唤醒词检测期望双通道数据，但数据处理逻辑有问题
3. **采样率处理**: ReadAudio方法在相同采样率下的双通道处理逻辑不完整

### 根本原因
```cpp
// 软件AEC启用后的配置
input_reference_ = true;     // 启用参考信号
input_channels_ = 2;         // 双通道：麦克风+参考信号

// AFE唤醒词检测配置
input_format = "MR";         // 麦克风+参考信号
GetFeedSize() = feed_chunksize * 2;  // 双倍数据大小
```

## 🔧 已修复的问题

### 1. ReadAudio方法双通道处理
**修复前**: 在相同采样率下忽略双通道处理
**修复后**: 正确处理软件AEC的双通道数据

```cpp
// 处理软件AEC的双通道数据
if (codec->input_channels() == 2) {
    data.resize(samples);  // samples已经包含了双通道大小
    if (!codec->InputData(data)) {
        return false;
    }
}
```

### 2. Read方法数据大小逻辑
**修复前**: 
```cpp
std::vector<int32_t> bit32_buffer(samples/2);  // 假设samples是双通道总数
return actual_samples*2;  // 返回双通道总数
```

**修复后**:
```cpp
int mic_samples = samples / 2;  // 明确计算单通道样本数
std::vector<int32_t> bit32_buffer(mic_samples);
return actual_mic_samples*2;  // 返回双通道总样本数
```

## 🧪 测试和验证

### 1. 编译测试
```bash
cd xiaozhi-esp32-otto-5.2
idf.py build
```

### 2. 运行时日志检查
启动设备后，检查以下日志：

#### AFE唤醒词初始化
```
I (xxx) AfeWakeWord: Model 0: [模型名称]
I (xxx) AfeWakeWord: Audio detection task started, feed size: [数值] fetch size: [数值]
```

#### 音频数据流
```
I (xxx) Application: Wake word feed size: [应该是双通道大小]
I (xxx) NoAudioCodec: Read samples: [双通道总样本数]
```

### 3. 功能测试步骤

#### 基础测试
1. **设备启动**: 确认设备正常启动到待机状态
2. **音频输入**: 确认麦克风工作正常
3. **音频输出**: 确认扬声器播放正常

#### 唤醒词测试
1. **静音环境**: 在安静环境下说"你好小智"
2. **播放时测试**: 播放音频时说"你好小智"（测试AEC效果）
3. **距离测试**: 在不同距离下测试唤醒词识别

### 4. 调试日志添加

如果问题仍然存在，可以添加调试日志：

#### 在AfeWakeWord::Feed中添加
```cpp
void AfeWakeWord::Feed(const std::vector<int16_t>& data) {
    if (afe_data_ == nullptr) {
        return;
    }
    ESP_LOGI(TAG, "Feed data size: %d, expected: %d", 
             data.size(), GetFeedSize());
    afe_iface_->feed(afe_data_, data.data());
}
```

#### 在NoAudioCodec::Read中添加
```cpp
#ifdef CONFIG_USE_DEVICE_AEC
    if (input_reference_) {
        ESP_LOGI(TAG, "AEC Read: requested=%d, mic_samples=%d, returned=%d", 
                 samples, mic_samples, actual_mic_samples*2);
        // ... 现有代码
    }
#endif
```

## 🎯 预期结果

### 修复后的预期行为
1. ✅ **唤醒词检测**: "你好小智"能正常唤醒设备
2. ✅ **AEC效果**: 播放时说话不会误触发，但真实语音能正确识别
3. ✅ **数据流**: AFE接收正确格式的双通道数据
4. ✅ **性能**: 唤醒词检测延迟在可接受范围内

### 可能的问题和解决方案

#### 问题1: 仍然无法唤醒
**可能原因**: 
- 麦克风硬件问题
- 音频增益过低
- 环境噪声过大

**解决方案**:
- 检查麦克风连接
- 调整音频增益设置
- 在安静环境下测试

#### 问题2: 误触发严重
**可能原因**:
- AEC效果不佳
- 参考信号不准确
- 播放音量过大

**解决方案**:
- 检查播放缓存同步
- 调整播放音量
- 优化AEC参数

#### 问题3: 检测延迟过大
**可能原因**:
- CPU负载过高
- 内存不足
- AFE配置不当

**解决方案**:
- 优化任务优先级
- 检查内存使用
- 调整AFE性能模式

## 📝 下一步行动

1. **编译并烧录**: 应用修复后的代码
2. **基础测试**: 验证音频输入输出正常
3. **唤醒词测试**: 测试"你好小智"识别
4. **AEC测试**: 测试播放时的语音识别
5. **性能测试**: 检查CPU和内存使用情况

如果问题仍然存在，请提供详细的日志信息以便进一步诊断。
