#ifndef _DUAL_MIC_AUDIO_CODEC_H
#define _DUAL_MIC_AUDIO_CODEC_H

#include "audio_codec.h"
#include <driver/i2s_std.h>

class DualMicAudioCodec : public AudioCodec {
public:
    DualMicAudioCodec(int input_sample_rate, int output_sample_rate,
                      gpio_num_t mclk, gpio_num_t bclk, gpio_num_t ws, 
                      gpio_num_t dout, gpio_num_t din0, gpio_num_t din1);
    virtual ~DualMicAudioCodec();
    
    virtual void SetOutputVolume(int volume) override;
    virtual void EnableInput(bool enable) override;
    virtual void EnableOutput(bool enable) override;
    virtual void Start() override;

protected:
    virtual int Read(int16_t* dest, int samples) override;
    virtual int Write(const int16_t* data, int samples) override;

private:
    void ConfigureI2S();
    
    gpio_num_t mclk_pin_;
    gpio_num_t bclk_pin_;
    gpio_num_t ws_pin_;
    gpio_num_t dout_pin_;
    gpio_num_t din0_pin_;
    gpio_num_t din1_pin_;
    
    i2s_chan_handle_t tx_handle_ = nullptr;
    i2s_chan_handle_t rx_handle_ = nullptr;
};

#endif // _DUAL_MIC_AUDIO_CODEC_H
