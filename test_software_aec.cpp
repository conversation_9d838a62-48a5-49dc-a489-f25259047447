/**
 * @file test_software_aec.cpp
 * @brief Otto Robot软件AEC功能测试程序
 * 
 * 本测试程序用于验证Otto Robot的软件AEC实现是否正常工作
 * 基于小智AI开发板的软件AEC方案进行适配
 */

#include <stdio.h>
#include <string.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <esp_log.h>
#include <esp_system.h>

#include "board.h"
#include "application.h"
#include "audio_processor.h"

static const char* TAG = "AEC_TEST";

/**
 * @brief 测试软件AEC功能
 */
void test_software_aec() {
    ESP_LOGI(TAG, "开始测试Otto Robot软件AEC功能");
    
    // 获取板级配置
    auto& board = Board::GetInstance();
    auto audio_codec = board.GetAudioCodec();
    
    ESP_LOGI(TAG, "音频编解码器信息:");
    ESP_LOGI(TAG, "  输入采样率: %d Hz", audio_codec->input_sample_rate());
    ESP_LOGI(TAG, "  输出采样率: %d Hz", audio_codec->output_sample_rate());
    ESP_LOGI(TAG, "  输入通道数: %d", audio_codec->input_channels());
    ESP_LOGI(TAG, "  支持参考信号: %s", audio_codec->input_reference() ? "是" : "否");
    
    // 检查AEC配置
#ifdef CONFIG_USE_DEVICE_AEC
    ESP_LOGI(TAG, "设备端AEC: 已启用");
#else
    ESP_LOGI(TAG, "设备端AEC: 未启用");
#endif

#ifdef CONFIG_USE_REALTIME_CHAT
    ESP_LOGI(TAG, "实时对话模式: 已启用");
#else
    ESP_LOGI(TAG, "实时对话模式: 未启用");
#endif

    // 验证音频处理器
    auto& app = Application::GetInstance();
    
    ESP_LOGI(TAG, "测试音频处理器初始化...");
    
    // 模拟播放一些音频数据
    std::vector<int16_t> test_audio(1600);  // 100ms @ 16kHz
    for (int i = 0; i < 1600; i++) {
        test_audio[i] = (int16_t)(sin(2 * M_PI * 1000 * i / 16000) * 16000);  // 1kHz正弦波
    }
    
    ESP_LOGI(TAG, "模拟播放测试音频...");
    audio_codec->Write(test_audio.data(), test_audio.size());
    
    // 模拟读取麦克风数据
    std::vector<int16_t> mic_data(3200);  // 双通道数据
    ESP_LOGI(TAG, "读取麦克风数据...");
    int samples_read = audio_codec->Read(mic_data.data(), mic_data.size());
    
    ESP_LOGI(TAG, "读取到 %d 个样本", samples_read);
    
    if (audio_codec->input_reference() && samples_read > 0) {
        ESP_LOGI(TAG, "验证双通道数据:");
        ESP_LOGI(TAG, "  麦克风通道 (偶数索引): %d, %d, %d...", 
                 mic_data[0], mic_data[2], mic_data[4]);
        ESP_LOGI(TAG, "  参考信号通道 (奇数索引): %d, %d, %d...", 
                 mic_data[1], mic_data[3], mic_data[5]);
    }
    
    ESP_LOGI(TAG, "软件AEC测试完成");
}

/**
 * @brief 测试AEC性能指标
 */
void test_aec_performance() {
    ESP_LOGI(TAG, "开始AEC性能测试");
    
    auto& board = Board::GetInstance();
    auto audio_codec = board.GetAudioCodec();
    
    const int test_duration_ms = 5000;  // 测试5秒
    const int frame_size = 320;  // 20ms @ 16kHz
    const int total_frames = test_duration_ms / 20;
    
    ESP_LOGI(TAG, "性能测试参数:");
    ESP_LOGI(TAG, "  测试时长: %d ms", test_duration_ms);
    ESP_LOGI(TAG, "  帧大小: %d 样本", frame_size);
    ESP_LOGI(TAG, "  总帧数: %d", total_frames);
    
    uint32_t start_time = esp_timer_get_time() / 1000;
    uint32_t total_processing_time = 0;
    
    for (int frame = 0; frame < total_frames; frame++) {
        uint32_t frame_start = esp_timer_get_time() / 1000;
        
        // 模拟音频处理
        std::vector<int16_t> audio_frame(frame_size * 2);  // 双通道
        audio_codec->Read(audio_frame.data(), audio_frame.size());
        
        uint32_t frame_end = esp_timer_get_time() / 1000;
        total_processing_time += (frame_end - frame_start);
        
        if (frame % 50 == 0) {  // 每秒输出一次进度
            ESP_LOGI(TAG, "处理进度: %d/%d 帧", frame, total_frames);
        }
        
        vTaskDelay(pdMS_TO_TICKS(20));  // 模拟20ms间隔
    }
    
    uint32_t end_time = esp_timer_get_time() / 1000;
    uint32_t total_time = end_time - start_time;
    
    ESP_LOGI(TAG, "性能测试结果:");
    ESP_LOGI(TAG, "  总耗时: %lu ms", total_time);
    ESP_LOGI(TAG, "  处理耗时: %lu ms", total_processing_time);
    ESP_LOGI(TAG, "  CPU使用率: %.2f%%", (float)total_processing_time / total_time * 100);
    ESP_LOGI(TAG, "  平均帧处理时间: %.2f ms", (float)total_processing_time / total_frames);
}

/**
 * @brief 主测试函数
 */
extern "C" void app_main() {
    ESP_LOGI(TAG, "Otto Robot软件AEC测试程序启动");
    
    // 初始化板级配置
    auto& board = Board::GetInstance();
    ESP_LOGI(TAG, "板型: %s", board.GetBoardType().c_str());
    
    // 等待系统稳定
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 执行基本功能测试
    test_software_aec();
    
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 执行性能测试
    test_aec_performance();
    
    ESP_LOGI(TAG, "所有测试完成");
    
    // 保持程序运行
    while (1) {
        vTaskDelay(pdMS_TO_TICKS(10000));
    }
}
