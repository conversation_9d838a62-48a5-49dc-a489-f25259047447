# 小智AI Otto机器人语音处理架构分析

## 项目概述

基于您的配置，这是一个ESP32-S3的Otto机器人项目，配置了：
- **板子类型**: otto-robot
- **麦克风**: INMP441 (I2S数字麦克风)
- **扬声器**: MAX98357A (I2S数字功放)
- **唤醒词检测**: AFE (Audio Front-End) 
- **音频降噪**: 启用
- **音频调试**: 启用
- **实时打断**: 支持

## 核心架构组件

### 1. 音频硬件配置 (main/boards/otto-robot/config.h)

```cpp
// 音频采样率配置
#define AUDIO_INPUT_SAMPLE_RATE 16000   // 麦克风输入采样率
#define AUDIO_OUTPUT_SAMPLE_RATE 24000  // 扬声器输出采样率

// I2S麦克风引脚 (INMP441)
#define AUDIO_I2S_MIC_GPIO_WS GPIO_NUM_4    // Word Select
#define AUDIO_I2S_MIC_GPIO_SCK GPIO_NUM_5   // Serial Clock
#define AUDIO_I2S_MIC_GPIO_DIN GPIO_NUM_6   // Data Input

// I2S扬声器引脚 (MAX98357A)
#define AUDIO_I2S_SPK_GPIO_DOUT GPIO_NUM_7  // Data Output
#define AUDIO_I2S_SPK_GPIO_BCLK GPIO_NUM_15 // Bit Clock
#define AUDIO_I2S_SPK_GPIO_LRCK GPIO_NUM_16 // Left/Right Clock
```

### 2. 音频编解码器 (main/boards/otto-robot/otto_robot.cc)

项目使用 `NoAudioCodecSimplex` 类，这是一个简化的I2S音频编解码器：

```cpp
virtual AudioCodec* GetAudioCodec() override {
    static NoAudioCodecSimplex audio_codec(
        AUDIO_INPUT_SAMPLE_RATE,     // 16kHz输入
        AUDIO_OUTPUT_SAMPLE_RATE,    // 24kHz输出
        AUDIO_I2S_SPK_GPIO_BCLK,     // 扬声器时钟
        AUDIO_I2S_SPK_GPIO_LRCK,     // 扬声器左右声道
        AUDIO_I2S_SPK_GPIO_DOUT,     // 扬声器数据输出
        AUDIO_I2S_MIC_GPIO_SCK,      // 麦克风时钟
        AUDIO_I2S_MIC_GPIO_WS,       // 麦克风字选择
        AUDIO_I2S_MIC_GPIO_DIN       // 麦克风数据输入
    );
    return &audio_codec;
}
```

### 3. AFE音频前端处理 (main/audio_processing/afe_audio_processor.cc)

AFE (Audio Front-End) 是ESP-SR组件的核心，提供：

#### 功能特性：
- **AEC (声学回声消除)**: 消除扬声器播放对麦克风的干扰
- **VAD (语音活动检测)**: 检测是否有人在说话
- **NS (噪声抑制)**: 降低环境噪声
- **AGC (自动增益控制)**: 自动调节音频增益

#### 配置参数：
```cpp
afe_config_t* afe_config = afe_config_init(input_format.c_str(), NULL, AFE_TYPE_VC, AFE_MODE_HIGH_PERF);
afe_config->aec_mode = AEC_MODE_VOIP_HIGH_PERF;  // 高性能AEC
afe_config->vad_mode = VAD_MODE_0;               // VAD模式
afe_config->vad_min_noise_ms = 100;             // 最小噪声持续时间
afe_config->afe_perferred_core = 1;             // 运行在核心1
afe_config->memory_alloc_mode = AFE_MEMORY_ALLOC_MORE_PSRAM; // 使用PSRAM
```

### 4. 唤醒词检测 (main/audio_processing/afe_wake_word.cc)

#### 当前配置的唤醒词：
根据sdkconfig，启用了 `CONFIG_SR_WN_WN9_NIHAOXIAOZHI_TTS=y`，即"你好小智"

#### 唤醒词检测流程：
1. **初始化**: 加载WakeNet9模型
2. **音频输入**: 持续接收16kHz音频数据
3. **检测处理**: AFE处理后的音频送入唤醒词检测引擎
4. **结果回调**: 检测到唤醒词时触发回调

```cpp
void AfeWakeWord::AudioDetectionTask() {
    while (true) {
        auto res = afe_iface_->fetch_with_delay(afe_data_, portMAX_DELAY);
        
        // 存储唤醒词数据用于后续编码
        StoreWakeWordData(res->data, res->data_size / sizeof(int16_t));
        
        if (res->wakeup_state == WAKENET_DETECTED) {
            StopDetection();
            last_detected_wake_word_ = wake_words_[res->wake_word_index - 1];
            
            if (wake_word_detected_callback_) {
                wake_word_detected_callback_(last_detected_wake_word_);
            }
        }
    }
}
```

## 语音处理流程

### 1. AI语音下发到扬声器播放

#### 流程路径：
```
服务器TTS音频 → Protocol::OnIncomingAudio() → audio_decode_queue_ → 
OpusDecoder → 重采样(16kHz→24kHz) → AudioCodec::OutputData() → 
I2S扬声器(MAX98357A)
```

#### 关键实现 (main/application.cc):
```cpp
// 接收服务器音频数据
protocol_->OnIncomingAudio([this](AudioStreamPacket&& packet) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (device_state_ == kDeviceStateSpeaking && audio_decode_queue_.size() < MAX_AUDIO_PACKETS_IN_QUEUE) {
        audio_decode_queue_.emplace_back(std::move(packet));
    }
});

// 音频输出处理
void Application::OnAudioOutput() {
    auto packet = std::move(audio_decode_queue_.front());
    
    // Opus解码
    std::vector<int16_t> pcm;
    opus_decoder_->Decode(std::move(packet.payload), pcm);
    
    // 重采样 16kHz → 24kHz
    if (opus_decoder_->sample_rate() != codec->output_sample_rate()) {
        std::vector<int16_t> resampled(target_size);
        output_resampler_.Process(pcm.data(), pcm.size(), resampled.data());
        pcm = std::move(resampled);
    }
    
    // 输出到扬声器
    codec->OutputData(pcm);
}
```

### 2. 麦克风接收人声处理

#### 流程路径：
```
I2S麦克风(INMP441) → AudioCodec::InputData() → 重采样(16kHz) → 
AFE处理(AEC/VAD/NS) → OpusEncoder → 发送到服务器
```

#### 关键实现：
```cpp
// 音频输入处理
void Application::OnAudioInput() {
    // 唤醒词检测
    if (wake_word_->IsDetectionRunning()) {
        if (ReadAudio(data, 16000, samples)) {
            wake_word_->Feed(data);  // 送入唤醒词检测
        }
    }
    
    // 语音通信处理
    if (audio_processor_->IsRunning()) {
        if (ReadAudio(data, 16000, samples)) {
            audio_processor_->Feed(data);  // 送入AFE处理
        }
    }
}

// AFE输出回调
audio_processor_->OnOutput([this](std::vector<int16_t>&& data) {
    // Opus编码并发送到服务器
    opus_encoder_->Encode(std::move(data), [this](std::vector<uint8_t>&& opus) {
        AudioStreamPacket packet;
        packet.payload = std::move(opus);
        protocol_->SendAudio(packet);
    });
});
```

## 核心功能实现分析

### 1. 唤醒词唤醒功能实现

#### 实现文件：
- **主要文件**: `main/audio_processing/afe_wake_word.cc`
- **配置文件**: `sdkconfig` (CONFIG_USE_AFE_WAKE_WORD=y)
- **应用逻辑**: `main/application.cc` (OnWakeWordDetected回调)

#### 实现原理：

**1. 唤醒词模型加载**：
```cpp
// 加载"你好小智"唤醒词模型
srmodel_list_t *models = esp_srmodel_init("model");
for (int i = 0; i < models->num; i++) {
    if (strstr(models->model_name[i], ESP_WN_PREFIX) != NULL) {
        wakenet_model_ = models->model_name[i];
        auto words = esp_srmodel_get_wake_words(models, wakenet_model_);
        // 解析支持的唤醒词
        std::stringstream ss(words);
        std::string word;
        while (std::getline(ss, word, ';')) {
            wake_words_.push_back(word);  // "你好小智"
        }
    }
}
```

**2. 持续音频检测**：
```cpp
void AfeWakeWord::AudioDetectionTask() {
    while (true) {
        // 等待检测启用
        xEventGroupWaitBits(event_group_, DETECTION_RUNNING_EVENT, pdFALSE, pdTRUE, portMAX_DELAY);

        // 从AFE获取处理后的音频
        auto res = afe_iface_->fetch_with_delay(afe_data_, portMAX_DELAY);

        // 存储音频数据用于后续发送到服务器
        StoreWakeWordData(res->data, res->data_size / sizeof(int16_t));

        // 检测到唤醒词
        if (res->wakeup_state == WAKENET_DETECTED) {
            StopDetection();
            last_detected_wake_word_ = wake_words_[res->wake_word_index - 1];

            // 触发唤醒回调
            if (wake_word_detected_callback_) {
                wake_word_detected_callback_(last_detected_wake_word_);
            }
        }
    }
}
```

**3. 唤醒词检测回调处理**：
```cpp
wake_word_->OnWakeWordDetected([this](const std::string& wake_word) {
    Schedule([this, &wake_word]() {
        if (device_state_ == kDeviceStateIdle) {
            // 空闲状态：开始对话
            wake_word_->EncodeWakeWordData();  // 编码唤醒词音频

            if (!protocol_->IsAudioChannelOpened()) {
                SetDeviceState(kDeviceStateConnecting);
                if (!protocol_->OpenAudioChannel()) {
                    wake_word_->StartDetection();
                    return;
                }
            }

            // 发送唤醒词音频到服务器
            AudioStreamPacket packet;
            while (wake_word_->GetWakeWordOpus(packet.payload)) {
                protocol_->SendAudio(packet);
            }
            protocol_->SendWakeWordDetected(wake_word);

            // 进入监听模式
            SetListeningMode(aec_mode_ == kAecOff ? kListeningModeAutoStop : kListeningModeRealtime);

        } else if (device_state_ == kDeviceStateSpeaking) {
            // 说话状态：实时打断
            AbortSpeaking(kAbortReasonWakeWordDetected);
        }
    });
});
```

### 2. 实时打断功能实现

#### 实现原理：

**1. 说话状态下的唤醒词检测**：
```cpp
case kDeviceStateSpeaking:
    display->SetStatus(Lang::Strings::SPEAKING);

    if (listening_mode_ != kListeningModeRealtime) {
        audio_processor_->Stop();
        // 只有AFE唤醒词可以在说话模式下检测
#if CONFIG_USE_AFE_WAKE_WORD
        wake_word_->StartDetection();  // 关键：在播放时继续检测唤醒词
#else
        wake_word_->StopDetection();
#endif
    }
    break;
```

**2. 打断处理逻辑**：
```cpp
void Application::AbortSpeaking(AbortReason reason) {
    ESP_LOGI(TAG, "Abort speaking");
    aborted_ = true;  // 设置中止标志
    protocol_->SendAbortSpeaking(reason);  // 通知服务器中止
}

// 协议层发送中止消息
void Protocol::SendAbortSpeaking(AbortReason reason) {
    std::string message = "{\"session_id\":\"" + session_id_ + "\",\"type\":\"abort\"";
    if (reason == kAbortReasonWakeWordDetected) {
        message += ",\"reason\":\"wake_word_detected\"";  // 标明是唤醒词打断
    }
    message += "}";
    SendText(message);
}
```

**3. 音频输出中止机制**：
```cpp
void Application::OnAudioOutput() {
    background_task_->Schedule([this, codec, packet = std::move(packet)]() mutable {
        busy_decoding_audio_ = false;
        if (aborted_) {
            return;  // 如果被中止，直接返回不播放
        }

        // 正常解码和播放流程
        std::vector<int16_t> pcm;
        opus_decoder_->Decode(std::move(packet.payload), pcm);
        codec->OutputData(pcm);
    });
}
```

### 3. 音频调试功能

#### 实现文件：`main/audio_processing/audio_debugger.cc`

```cpp
void AudioDebugger::Feed(const std::vector<int16_t>& data) {
#if CONFIG_USE_AUDIO_DEBUGGER
    if (udp_sockfd_ >= 0) {
        // 通过UDP发送原始音频数据到调试服务器
        ssize_t sent = sendto(udp_sockfd_, data.data(), data.size() * sizeof(int16_t), 0,
                             (struct sockaddr*)&udp_server_addr_, sizeof(udp_server_addr_));
    }
#endif
}
```

配置的调试服务器：`CONFIG_AUDIO_DEBUG_UDP_SERVER="192.168.2.100:8000"`

### 4. 设备状态管理

#### 状态转换图：
```
kDeviceStateIdle (空闲)
    ↓ 唤醒词检测
kDeviceStateConnecting (连接中)
    ↓ 连接成功
kDeviceStateListening (监听中)
    ↓ 收到TTS开始
kDeviceStateSpeaking (播放中)
    ↓ TTS结束 或 唤醒词打断
kDeviceStateIdle (返回空闲)
```

#### 关键状态处理：
```cpp
void Application::SetDeviceState(DeviceState state) {
    switch (state) {
        case kDeviceStateIdle:
            audio_processor_->Stop();
            wake_word_->StartDetection();  // 开启唤醒词检测
            break;

        case kDeviceStateListening:
            if (!audio_processor_->IsRunning()) {
                audio_processor_->Start();   // 开启音频处理
                wake_word_->StopDetection(); // 停止唤醒词检测
            }
            break;

        case kDeviceStateSpeaking:
            if (listening_mode_ != kListeningModeRealtime) {
                audio_processor_->Stop();
#if CONFIG_USE_AFE_WAKE_WORD
                wake_word_->StartDetection(); // 播放时保持唤醒词检测
#endif
            }
            break;
    }
}
```

## 总结

### 唤醒词唤醒实现要点：

1. **硬件基础**: INMP441麦克风通过I2S接口持续采集16kHz音频
2. **AFE处理**: ESP-SR的AFE组件进行音频前端处理（降噪、回声消除等）
3. **WakeNet9模型**: 使用"你好小智"唤醒词模型进行本地检测
4. **状态管理**: 检测到唤醒词后从空闲状态转换到连接/监听状态
5. **音频上传**: 将唤醒词前后的音频编码后发送给服务器

### 实时打断实现要点：

1. **并行检测**: 在播放TTS时，AFE唤醒词检测继续运行
2. **快速响应**: 检测到唤醒词立即设置aborted_标志
3. **协议通知**: 向服务器发送abort消息，说明打断原因
4. **音频停止**: 后续音频包被丢弃，不再播放
5. **状态切换**: 从播放状态快速切换到监听状态

### 技术优势：

- **低延迟**: 本地唤醒词检测，无需网络往返
- **高精度**: AFE音频前端处理提升检测准确性
- **实时性**: 支持播放过程中的即时打断
- **调试友好**: UDP音频调试功能便于问题排查
