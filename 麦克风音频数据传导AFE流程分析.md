# 麦克风音频数据传导到AFE/AEC的完整流程分析

## 🎯 完整数据流路径

```
INMP441麦克风 → I2S硬件接口 → AudioCodec::InputData() → Application::ReadAudio() → 
Application::OnAudioInput() → AFE处理器 → AFE/AEC引擎
```

## 📍 关键函数位置和处理流程

### 1. 音频输入主循环 (`main/application.cc:880-926`)

#### 1.1 OnAudioInput() - 音频输入调度器
```cpp
void Application::OnAudioInput() {
    // 优先级1: 音频测试模式
    if (device_state_ == kDeviceStateAudioTesting) {
        // 处理音频测试...
        return;
    }

    // 优先级2: 唤醒词检测
    if (wake_word_->IsDetectionRunning()) {
        std::vector<int16_t> data;
        int samples = wake_word_->GetFeedSize();  // 🔥 获取AFE需要的样本数
        if (samples > 0) {
            if (ReadAudio(data, 16000, samples)) {  // 🔥 读取音频数据
                wake_word_->Feed(data);  // 🔥 送入AFE唤醒词检测
                return;
            }
        }
    }

    // 优先级3: 语音通信处理
    if (audio_processor_->IsRunning()) {
        std::vector<int16_t> data;
        int samples = audio_processor_->GetFeedSize();  // 🔥 获取AFE需要的样本数
        if (samples > 0) {
            if (ReadAudio(data, 16000, samples)) {  // 🔥 读取音频数据
                audio_processor_->Feed(data);  // 🔥 送入AFE音频处理器
                return;
            }
        }
    }

    vTaskDelay(pdMS_TO_TICKS(OPUS_FRAME_DURATION_MS / 2));  // 等待下次处理
}
```

**关键点**:
- **位置**: `main/application.cc:880-926`
- **调用频率**: 持续循环调用
- **优先级**: 唤醒词检测 > 语音通信处理
- **数据流向**: 根据设备状态决定数据去向

### 2. 音频数据读取 (`main/application.cc:928-973`)

#### 2.1 ReadAudio() - 音频数据获取和预处理
```cpp
bool Application::ReadAudio(std::vector<int16_t>& data, int sample_rate, int samples) {
    auto codec = Board::GetInstance().GetAudioCodec();  // 🔥 获取音频编解码器
    if (!codec->input_enabled()) {
        return false;
    }

    if (codec->input_sample_rate() != sample_rate) {
        // 需要重采样的情况
        data.resize(samples * codec->input_sample_rate() / sample_rate);
        if (!codec->InputData(data)) {  // 🔥 从硬件读取原始音频
            return false;
        }
        
        if (codec->input_channels() == 2) {
            // 双通道处理 (麦克风 + 参考信号)
            auto mic_channel = std::vector<int16_t>(data.size() / 2);
            auto reference_channel = std::vector<int16_t>(data.size() / 2);
            
            // 分离通道数据
            for (size_t i = 0, j = 0; i < mic_channel.size(); ++i, j += 2) {
                mic_channel[i] = data[j];          // 麦克风通道
                reference_channel[i] = data[j + 1]; // 参考信号通道
            }
            
            // 分别重采样
            auto resampled_mic = std::vector<int16_t>(input_resampler_.GetOutputSamples(mic_channel.size()));
            auto resampled_reference = std::vector<int16_t>(reference_resampler_.GetOutputSamples(reference_channel.size()));
            input_resampler_.Process(mic_channel.data(), mic_channel.size(), resampled_mic.data());
            reference_resampler_.Process(reference_channel.data(), reference_channel.size(), resampled_reference.data());
            
            // 重新交错排列
            data.resize(resampled_mic.size() + resampled_reference.size());
            for (size_t i = 0, j = 0; i < resampled_mic.size(); ++i, j += 2) {
                data[j] = resampled_mic[i];        // M通道
                data[j + 1] = resampled_reference[i]; // R通道
            }
        } else {
            // 单通道重采样
            auto resampled = std::vector<int16_t>(input_resampler_.GetOutputSamples(data.size()));
            input_resampler_.Process(data.data(), data.size(), resampled.data());
            data = std::move(resampled);
        }
    } else {
        // 无需重采样，直接读取
        data.resize(samples);
        if (!codec->InputData(data)) {  // 🔥 从硬件读取音频数据
            return false;
        }
    }
    
    // 音频调试：发送原始音频数据到UDP服务器
    if (audio_debugger_) {
        audio_debugger_->Feed(data);  // 🔥 调试数据输出
    }
    
    return true;
}
```

**关键点**:
- **位置**: `main/application.cc:928-973`
- **硬件接口**: `codec->InputData(data)` - 从I2S读取原始音频
- **数据处理**: 支持单/双通道，自动重采样
- **输出格式**: 16kHz, int16_t, 通道交错排列

### 3. AFE数据输入接口

#### 3.1 唤醒词检测器 (`main/audio_processing/afe_wake_word.cc:99-104`)
```cpp
void AfeWakeWord::Feed(const std::vector<int16_t>& data) {
    if (afe_data_ == nullptr) {
        return;
    }
    afe_iface_->feed(afe_data_, data.data());  // 🔥 送入AFE引擎
}
```

#### 3.2 音频处理器 (`main/audio_processing/afe_audio_processor.cc:82-87`)
```cpp
void AfeAudioProcessor::Feed(const std::vector<int16_t>& data) {
    if (afe_data_ == nullptr) {
        return;
    }
    afe_iface_->feed(afe_data_, data.data());  // 🔥 送入AFE引擎
}
```

**关键点**:
- **位置**: AFE处理器和唤醒词检测器
- **接口函数**: `afe_iface_->feed(afe_data_, data.data())`
- **数据要求**: int16_t*, 16kHz, 通道交错格式

## 🔄 AFE数据处理和输出

### 4. AFE处理任务

#### 4.1 唤醒词检测任务 (`main/audio_processing/afe_wake_word.cc:113-139`)
```cpp
void AfeWakeWord::AudioDetectionTask() {
    while (true) {
        // 等待检测启用
        xEventGroupWaitBits(event_group_, DETECTION_RUNNING_EVENT, pdFALSE, pdTRUE, portMAX_DELAY);

        // 从AFE获取处理结果
        auto res = afe_iface_->fetch_with_delay(afe_data_, portMAX_DELAY);  // 🔥 AFE输出

        // 存储音频数据用于后续编码
        StoreWakeWordData(res->data, res->data_size / sizeof(int16_t));

        // 检测唤醒词
        if (res->wakeup_state == WAKENET_DETECTED) {
            StopDetection();
            last_detected_wake_word_ = wake_words_[res->wake_word_index - 1];

            // 触发唤醒回调
            if (wake_word_detected_callback_) {
                wake_word_detected_callback_(last_detected_wake_word_);
            }
        }
    }
}
```

#### 4.2 音频处理任务 (`main/audio_processing/afe_audio_processor.cc:112-147`)
```cpp
void AfeAudioProcessor::AudioProcessorTask() {
    while (true) {
        // 等待处理器启用
        xEventGroupWaitBits(event_group_, PROCESSOR_RUNNING, pdFALSE, pdTRUE, portMAX_DELAY);

        // 从AFE获取处理结果
        auto res = afe_iface_->fetch_with_delay(afe_data_, portMAX_DELAY);  // 🔥 AFE输出

        // VAD状态变化检测
        if (vad_state_change_callback_) {
            if (res->vad_state == VAD_SPEECH && !is_speaking_) {
                is_speaking_ = true;
                vad_state_change_callback_(true);  // 🔥 语音活动开始
            } else if (res->vad_state == VAD_SILENCE && is_speaking_) {
                is_speaking_ = false;
                vad_state_change_callback_(false); // 🔥 语音活动结束
            }
        }

        // 处理后的音频数据输出
        if (output_callback_) {
            output_callback_(std::vector<int16_t>(res->data, res->data + res->data_size / sizeof(int16_t)));
        }
    }
}
```

## 📊 数据格式和时序分析

### 5. 数据块大小管理

#### 5.1 AFE数据块大小获取
```cpp
// 唤醒词检测器
size_t AfeWakeWord::GetFeedSize() {
    if (afe_data_ == nullptr) {
        return 0;
    }
    return afe_iface_->get_feed_chunksize(afe_data_) * codec_->input_channels();  // 🔥 AFE需要的样本数
}

// 音频处理器
size_t AfeAudioProcessor::GetFeedSize() {
    if (afe_data_ == nullptr) {
        return 0;
    }
    return afe_iface_->get_feed_chunksize(afe_data_) * codec_->input_channels();  // 🔥 AFE需要的样本数
}
```

**关键点**:
- **数据块大小**: 由AFE引擎决定 (`get_feed_chunksize`)
- **通道倍数**: 乘以输入通道数 (`codec_->input_channels()`)
- **当前配置**: 单通道 (M), 每次处理固定样本数

### 6. 当前Otto机器人的具体配置

#### 6.1 硬件配置
- **麦克风**: INMP441 (I2S数字麦克风)
- **采样率**: 16kHz
- **位深度**: 16位
- **通道数**: 1 (单麦克风)

#### 6.2 AFE配置
- **输入格式**: "M" (单麦克风，无参考信号)
- **AEC状态**: 未启用 (因为没有参考信号)
- **处理功能**: VAD + 唤醒词检测 + 噪声抑制

#### 6.3 数据流时序
```
每个音频帧 (通常16ms):
INMP441 → I2S → AudioCodec → ReadAudio → OnAudioInput → AFE.feed → AFE处理 → AFE.fetch → 回调处理
```

## 🚨 当前问题分析

### 7. 缺失的参考信号路径

#### 7.1 问题描述
- **麦克风路径**: 正常工作，数据正确传导到AFE
- **参考信号路径**: **完全缺失**，TTS音频未送入AFE
- **结果**: AEC无法工作，扬声器音频被误识别为唤醒词

#### 7.2 解决方案要点
需要在TTS音频输出时，同时将音频数据送入AFE作为参考信号：

```cpp
// 在 Application::OnAudioOutput() 中添加
codec->OutputData(pcm);  // 现有扬声器输出

// 🔥 新增: 同时送入AFE作为参考信号
if (wake_word_->IsDetectionRunning() || audio_processor_->IsRunning()) {
    FeedReferenceSignalToAFE(pcm);  // 需要实现此函数
}
```
