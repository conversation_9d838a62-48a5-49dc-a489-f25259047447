#!/bin/bash

# Otto Robot 软件AEC编译和测试脚本
# 基于小智AI开发板软件AEC方案

set -e  # 遇到错误立即退出

echo "=========================================="
echo "Otto Robot 软件AEC编译和测试脚本"
echo "=========================================="

# 检查环境
echo "1. 检查编译环境..."
if ! command -v idf.py &> /dev/null; then
    echo "错误: 未找到idf.py命令，请确保ESP-IDF环境已正确设置"
    exit 1
fi

echo "ESP-IDF版本信息:"
idf.py --version

# 清理之前的构建
echo ""
echo "2. 清理之前的构建..."
if [ -d "build" ]; then
    rm -rf build
    echo "已清理build目录"
fi

# 设置目标芯片
echo ""
echo "3. 设置目标芯片为ESP32-S3..."
idf.py set-target esp32s3

# 配置项目
echo ""
echo "4. 配置项目..."
echo "请在menuconfig中确保以下选项已启用:"
echo "  - Xiaozhi Assistant -> Board Type -> ottoRobot"
echo "  - Xiaozhi Assistant -> Enable Device-Side AEC"
echo "  - Xiaozhi Assistant -> 启用可语音打断的实时对话模式"
echo ""
read -p "是否需要打开menuconfig进行配置? (y/N): " open_menuconfig

if [[ $open_menuconfig =~ ^[Yy]$ ]]; then
    idf.py menuconfig
fi

# 编译项目
echo ""
echo "5. 编译项目..."
echo "开始编译，这可能需要几分钟时间..."
if idf.py build; then
    echo "✅ 编译成功!"
else
    echo "❌ 编译失败，请检查错误信息"
    exit 1
fi

# 检查关键配置
echo ""
echo "6. 检查关键配置..."
if grep -q "CONFIG_USE_DEVICE_AEC=y" build/config/sdkconfig; then
    echo "✅ 设备端AEC已启用"
else
    echo "⚠️  设备端AEC未启用，请检查配置"
fi

if grep -q "CONFIG_USE_REALTIME_CHAT=y" build/config/sdkconfig; then
    echo "✅ 实时对话模式已启用"
else
    echo "⚠️  实时对话模式未启用，请检查配置"
fi

if grep -q "CONFIG_BOARD_TYPE_OTTO_ROBOT=y" build/config/sdkconfig; then
    echo "✅ Otto Robot板型已选择"
else
    echo "⚠️  Otto Robot板型未选择，请检查配置"
fi

# 分析编译结果
echo ""
echo "7. 分析编译结果..."
echo "固件大小信息:"
idf.py size

echo ""
echo "关键组件大小:"
idf.py size-components | grep -E "(audio|aec|afe|esp-sr)" || echo "未找到音频相关组件信息"

# 提供烧录指令
echo ""
echo "8. 烧录指令..."
echo "要烧录到设备，请使用以下命令:"
echo "  idf.py flash"
echo ""
echo "要烧录并监控串口输出:"
echo "  idf.py flash monitor"
echo ""
echo "仅监控串口输出:"
echo "  idf.py monitor"

# 提供测试建议
echo ""
echo "9. 测试建议..."
echo "烧录完成后，可以通过以下方式测试AEC功能:"
echo ""
echo "a) 观察启动日志，确认以下信息:"
echo "   - 音频编解码器初始化成功"
echo "   - AEC模式设置为高性能模式"
echo "   - 双通道音频处理启用"
echo ""
echo "b) 测试实时对话功能:"
echo "   - 播放音乐时说话，观察是否能正常识别"
echo "   - 测试语音打断功能"
echo ""
echo "c) 检查性能指标:"
echo "   - CPU使用率应保持在合理范围"
echo "   - 内存使用正常"
echo "   - 音频延迟可接受"

# 提供调试信息
echo ""
echo "10. 调试信息..."
echo "如果遇到问题，可以启用详细日志:"
echo "在main/main.cpp中添加:"
echo '  esp_log_level_set("AFE_AUDIO_PROCESSOR", ESP_LOG_DEBUG);'
echo '  esp_log_level_set("NO_AUDIO_CODEC", ESP_LOG_DEBUG);'
echo '  esp_log_level_set("APPLICATION", ESP_LOG_DEBUG);'

echo ""
echo "=========================================="
echo "编译脚本执行完成!"
echo "=========================================="
echo ""
echo "下一步操作:"
echo "1. 连接Otto Robot设备到电脑"
echo "2. 运行: idf.py flash monitor"
echo "3. 观察启动日志，确认AEC功能正常"
echo "4. 测试语音交互功能"
echo ""
echo "如有问题，请参考 Otto_Robot_软件AEC实现说明.md 文档"
