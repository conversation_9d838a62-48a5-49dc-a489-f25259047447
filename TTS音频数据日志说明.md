# TTS音频数据日志追踪说明

## 📋 已添加的日志点

为了追踪每次下发的TTS音频数据，我在以下关键位置添加了详细的日志输出：

### 1. WebSocket协议层 (`main/protocols/websocket_protocol.cc`)

#### 1.1 WebSocket原始数据接收
```cpp
ESP_LOGI("WebsocketProtocol", "📡 WebSocket Binary Data: len=%d, version=%d", len, version_);
```
**位置**: 第117行
**作用**: 记录WebSocket接收到的原始二进制数据长度和协议版本

#### 1.2 Protocol2解析
```cpp
ESP_LOGI("WebsocketProtocol", "📦 Protocol2 Parsed: type=%d, timestamp=%u, payload_size=%u", 
    bp2->type, bp2->timestamp, bp2->payload_size);
```
**位置**: 第127-128行
**作用**: 记录Protocol2格式的解析结果

#### 1.3 Protocol3解析
```cpp
ESP_LOGI("WebsocketProtocol", "📦 Protocol3 Parsed: type=%d, payload_size=%u", 
    bp3->type, bp3->payload_size);
```
**位置**: 第139-140行
**作用**: 记录Protocol3格式的解析结果

### 2. 应用层音频处理 (`main/application.cc`)

#### 2.1 TTS命令接收
```cpp
ESP_LOGI(TAG, "🎤 TTS Start Command Received");
ESP_LOGI(TAG, "🛑 TTS Stop Command Received");
```
**位置**: 第517行, 第527行
**作用**: 记录TTS开始和停止命令的接收

#### 2.2 设备状态变化
```cpp
ESP_LOGI(TAG, "🎤 TTS Starting: device_state %d → kDeviceStateSpeaking", device_state_);
ESP_LOGI(TAG, "🛑 TTS Stopped: kDeviceStateSpeaking → kDeviceStateIdle");
ESP_LOGI(TAG, "🛑 TTS Stopped: kDeviceStateSpeaking → kDeviceStateListening");
```
**位置**: 第521行, 第533行, 第536行
**作用**: 记录设备状态的转换过程

#### 2.3 音频数据入队
```cpp
ESP_LOGI(TAG, "📥 TTS Audio Received: sample_rate=%d, frame_duration=%d, payload_size=%d, queue_size=%d", 
    packet.sample_rate, packet.frame_duration, packet.payload.size(), audio_decode_queue_.size());
```
**位置**: 第481-482行
**作用**: 记录每个音频包的接收和队列状态

#### 2.4 音频数据丢弃
```cpp
ESP_LOGW(TAG, "🚫 TTS Audio Dropped: device_state=%d, queue_size=%d, payload_size=%d", 
    device_state_, audio_decode_queue_.size(), packet.payload.size());
```
**位置**: 第484-485行
**作用**: 记录被丢弃的音频包及原因

#### 2.5 音频处理开始
```cpp
ESP_LOGI(TAG, "🎵 TTS Audio Processing: sample_rate=%d, frame_duration=%d, payload_size=%d, remaining_queue=%d", 
    packet.sample_rate, packet.frame_duration, packet.payload.size(), audio_decode_queue_.size());
```
**位置**: 第827-828行
**作用**: 记录开始处理的音频包信息和剩余队列长度

#### 2.6 Opus解码结果
```cpp
ESP_LOGE(TAG, "❌ TTS Opus Decode Failed");
ESP_LOGI(TAG, "🔊 TTS Opus Decoded: pcm_samples=%d, decoder_rate=%d", 
    pcm.size(), opus_decoder_->sample_rate());
```
**位置**: 第845行, 第848-849行
**作用**: 记录Opus解码的成功/失败状态和PCM数据信息

#### 2.7 重采样处理
```cpp
ESP_LOGI(TAG, "🔄 TTS Resampled: %d→%d Hz, samples %d→%d", 
    opus_decoder_->sample_rate(), codec->output_sample_rate(), pcm.size(), target_size);
```
**位置**: 第855-856行
**作用**: 记录重采样的详细信息（采样率转换和样本数变化）

#### 2.8 扬声器输出
```cpp
ESP_LOGI(TAG, "🔈 TTS Output to Speaker: samples=%d, output_rate=%d", 
    pcm.size(), codec->output_sample_rate());
```
**位置**: 第860-861行
**作用**: 记录最终输出到扬声器的PCM数据信息

## 📊 日志输出示例

当TTS音频播放时，您将看到类似以下的日志序列：

```
I (12345) WebsocketProtocol: 📡 WebSocket Binary Data: len=156, version=2
I (12346) WebsocketProtocol: 📦 Protocol2 Parsed: type=0, timestamp=1234567890, payload_size=148
I (12347) Application: 🎤 TTS Start Command Received
I (12348) Application: 🎤 TTS Starting: device_state 2 → kDeviceStateSpeaking
I (12349) Application: 📥 TTS Audio Received: sample_rate=16000, frame_duration=60, payload_size=148, queue_size=0
I (12350) Application: 🎵 TTS Audio Processing: sample_rate=16000, frame_duration=60, payload_size=148, remaining_queue=0
I (12351) Application: 🔊 TTS Opus Decoded: pcm_samples=960, decoder_rate=16000
I (12352) Application: 🔄 TTS Resampled: 16000→24000 Hz, samples 960→1440
I (12353) Application: 🔈 TTS Output to Speaker: samples=1440, output_rate=24000
```

## 🔍 日志分析要点

1. **数据完整性**: 检查payload_size是否合理（通常几十到几百字节）
2. **队列状态**: 观察queue_size变化，确认没有积压或丢包
3. **采样率转换**: 确认16kHz→24kHz的重采样正常工作
4. **时序关系**: 确认TTS命令和音频数据的时序关系
5. **错误检测**: 关注❌标记的错误日志

## 🎯 用于AEC调试

这些日志特别有助于：
- 确定TTS音频数据的确切时机和内容
- 为AEC参考信号实现提供数据基础
- 调试自我打断问题的根本原因
- 验证音频处理流程的正确性

---

# AFE/AEC接收处理函数位置和数据格式分析

## 📍 AFE/AEC核心处理函数位置

### 1. AFE音频处理器 (`main/audio_processing/afe_audio_processor.cc`)

#### 1.1 数据输入函数
```cpp
void AfeAudioProcessor::Feed(const std::vector<int16_t>& data) {
    if (afe_data_ == nullptr) {
        return;
    }
    afe_iface_->feed(afe_data_, data.data());  // 🔥 核心输入函数
}
```
**位置**: 第82-87行
**作用**: 将音频数据送入AFE处理引擎

#### 1.2 数据输出处理
```cpp
void AfeAudioProcessor::AudioProcessorTask() {
    while (true) {
        auto res = afe_iface_->fetch_with_delay(afe_data_, portMAX_DELAY);  // 🔥 核心输出函数

        // VAD状态变化检测
        if (vad_state_change_callback_) {
            if (res->vad_state == VAD_SPEECH && !is_speaking_) {
                is_speaking_ = true;
                vad_state_change_callback_(true);
            }
        }

        // 处理后的音频数据输出
        if (output_callback_) {
            output_callback_(std::vector<int16_t>(res->data, res->data + res->data_size / sizeof(int16_t)));
        }
    }
}
```
**位置**: 第112-147行
**作用**: 从AFE获取处理后的音频数据和VAD状态

### 2. AFE唤醒词检测器 (`main/audio_processing/afe_wake_word.cc`)

#### 2.1 数据输入函数
```cpp
void AfeWakeWord::Feed(const std::vector<int16_t>& data) {
    if (afe_data_ == nullptr) {
        return;
    }
    afe_iface_->feed(afe_data_, data.data());  // 🔥 核心输入函数
}
```
**位置**: 第99-104行
**作用**: 将音频数据送入AFE唤醒词检测引擎

#### 2.2 唤醒词检测处理
```cpp
void AfeWakeWord::AudioDetectionTask() {
    while (true) {
        auto res = afe_iface_->fetch_with_delay(afe_data_, portMAX_DELAY);  // 🔥 核心输出函数

        // 存储唤醒词数据
        StoreWakeWordData(res->data, res->data_size / sizeof(int16_t));

        // 检测唤醒词
        if (res->wakeup_state == WAKENET_DETECTED) {
            last_detected_wake_word_ = wake_words_[res->wake_word_index - 1];
            if (wake_word_detected_callback_) {
                wake_word_detected_callback_(last_detected_wake_word_);
            }
        }
    }
}
```
**位置**: 第113-139行
**作用**: 检测唤醒词并触发回调

## 📊 AFE/AEC数据格式要求

### 1. 输入数据格式 (ESP-SR接口定义)

根据`esp_afe_sr_iface.h`第91-100行的定义：

```cpp
/**
 * @brief Feed samples of an audio stream to the AFE_SR
 *
 * @Warning  The input data should be arranged in the format of channel interleaving.
 *           The last channel is reference signal if it has reference data.
 *
 * @param afe   The AFE_SR object to query
 * @param in    The input microphone signal, only support signed 16-bit @ 16 KHZ.
 *              The frame size can be queried by the `get_feed_chunksize`.
 * @return      The size of input
 */
typedef int (*esp_afe_sr_iface_op_feed_t)(esp_afe_sr_data_t *afe, const int16_t *in);
```

#### 关键要求：
- **数据类型**: `int16_t*` (16位有符号整数)
- **采样率**: 16kHz (固定要求)
- **通道格式**: 通道交错排列 (Channel Interleaving)
- **参考信号**: 最后一个通道是参考信号 (如果有的话)

### 2. 通道交错格式说明

#### 2.1 单麦克风 (无参考信号)
```
输入格式: "M"
数据排列: [M0, M1, M2, M3, ...]
```

#### 2.2 单麦克风 + 参考信号
```
输入格式: "MR"
数据排列: [M0, R0, M1, R1, M2, R2, ...]
其中: M = 麦克风数据, R = 参考信号数据
```

#### 2.3 双麦克风 + 参考信号
```
输入格式: "MMR"
数据排列: [M0_0, M1_0, R0, M0_1, M1_1, R1, ...]
其中: M0 = 第一个麦克风, M1 = 第二个麦克风, R = 参考信号
```

### 3. 当前项目的AFE配置

#### 3.1 AFE音频处理器配置 (`afe_audio_processor.cc` 第13-29行)
```cpp
void AfeAudioProcessor::Initialize(AudioCodec* codec) {
    codec_ = codec;
    int ref_num = codec_->input_reference() ? 1 : 0;  // 🔥 检查是否有参考信号

    std::string input_format;
    for (int i = 0; i < codec_->input_channels() - ref_num; i++) {
        input_format.push_back('M');  // 添加麦克风通道
    }
    for (int i = 0; i < ref_num; i++) {
        input_format.push_back('R');  // 添加参考信号通道
    }

    // 当前Otto机器人: input_format = "M" (单麦克风，无参考信号)
}
```

#### 3.2 AFE唤醒词检测配置 (`afe_wake_word.cc` 第33-68行)
```cpp
void AfeWakeWord::Initialize(AudioCodec* codec) {
    int ref_num = codec_->input_reference() ? 1 : 0;  // 🔥 检查是否有参考信号

    std::string input_format;
    for (int i = 0; i < codec_->input_channels() - ref_num; i++) {
        input_format.push_back('M');  // 麦克风通道
    }
    for (int i = 0; i < ref_num; i++) {
        input_format.push_back('R');  // 参考信号通道
    }

    afe_config_t* afe_config = afe_config_init(input_format.c_str(), models, AFE_TYPE_SR, AFE_MODE_HIGH_PERF);
    afe_config->aec_init = codec_->input_reference();  // 🔥 AEC初始化取决于是否有参考信号
}
```

## 🚨 当前问题分析

### 1. 音频编解码器问题

#### 1.1 当前使用的编解码器
```cpp
// main/boards/otto-robot/otto_robot.cc 第116-122行
virtual AudioCodec* GetAudioCodec() override {
    static NoAudioCodecSimplex audio_codec(
        AUDIO_INPUT_SAMPLE_RATE,     // 16kHz输入
        AUDIO_OUTPUT_SAMPLE_RATE,    // 24kHz输出
        // ... 其他参数
    );
    return &audio_codec;
}
```

#### 1.2 问题分析
- **编解码器类型**: `NoAudioCodecSimplex` (简单模式)
- **参考信号支持**: `codec_->input_reference()` 返回 `false`
- **输入通道数**: `codec_->input_channels()` 返回 `1`
- **结果**: AFE配置为 `input_format = "M"`，没有参考信号通道

### 2. AFE配置问题

#### 2.1 当前AFE配置结果
```cpp
// 由于 codec_->input_reference() == false
input_format = "M"                    // 只有麦克风，无参考信号
afe_config->aec_init = false         // AEC未初始化
```

#### 2.2 问题后果
- AFE只接收麦克风输入，没有扬声器参考信号
- AEC功能未启用，无法进行回声消除
- 扬声器播放的TTS音频会被麦克风拾取，误触发唤醒词

### 3. 数据流问题

#### 3.1 当前数据流
```
麦克风 → AFE (只有M通道) → 唤醒词检测 ❌ 误触发
扬声器 ← TTS音频 (独立路径，未送入AFE)
```

#### 3.2 期望数据流
```
麦克风 → AFE (M通道) → 唤醒词检测 ✅ 正确检测
扬声器 ← TTS音频 → AFE (R通道) → AEC处理
```

## 🔧 解决方案

### 1. 修改音频编解码器

需要修改`NoAudioCodecSimplex`或创建新的编解码器类，使其：
- `input_reference()` 返回 `true`
- `input_channels()` 返回 `2` (麦克风 + 参考信号)

### 2. 添加参考信号输入

在TTS音频输出时，同时将PCM数据作为参考信号送入AFE：

```cpp
// 在 Application::OnAudioOutput() 中添加
codec->OutputData(pcm);  // 现有输出

// 🔥 新增: 将TTS音频作为参考信号送入AFE
if (wake_word_->IsDetectionRunning() || audio_processor_->IsRunning()) {
    FeedReferenceSignal(pcm);  // 需要实现
}
```

### 3. 实现参考信号处理

需要实现一个机制，将24kHz的TTS PCM数据：
1. 重采样到16kHz
2. 与麦克风数据交错排列
3. 送入AFE的feed函数

### 4. 数据格式转换

```cpp
// 伪代码示例
void FeedReferenceSignal(const std::vector<int16_t>& speaker_pcm) {
    // 1. 重采样 24kHz → 16kHz
    std::vector<int16_t> resampled_ref = ResampleTo16kHz(speaker_pcm);

    // 2. 与麦克风数据交错
    std::vector<int16_t> interleaved_data;
    for (size_t i = 0; i < mic_data.size(); ++i) {
        interleaved_data.push_back(mic_data[i]);      // M通道
        interleaved_data.push_back(resampled_ref[i]); // R通道
    }

    // 3. 送入AFE
    afe_iface_->feed(afe_data_, interleaved_data.data());
}
```
```
