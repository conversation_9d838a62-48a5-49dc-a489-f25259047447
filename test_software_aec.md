# Otto-Robot 软件AEC测试指南

## 实现概述

我们为otto-robot实现了软件AEC功能，主要修改包括：

### 1. 配置修改
- **采样率统一**: 将输出采样率从24kHz改为16kHz (`main/boards/otto-robot/config.h`)
- **AEC支持**: 在Kconfig中为otto-robot添加AEC支持 (`main/Kconfig.projbuild`)
- **配置启用**: otto-robot配置文件中已启用 `CONFIG_USE_DEVICE_AEC=y`

### 2. 核心实现 (`main/audio_codecs/no_audio_codec.cc/h`)

#### 虚拟参考信号
```cpp
#ifdef CONFIG_USE_DEVICE_AEC
    input_reference_ = true;        // 启用虚拟参考信号
    input_channels_ = 2;            // 双通道：麦克风+参考信号
    std::vector<int16_t> output_buffer_;  // 播放缓存
#endif
```

#### 播放缓存 (Write方法)
- 缓存播放数据到 `output_buffer_` (1.6秒缓存)
- 线程安全的循环缓存机制
- 保持原有音频输出功能

#### 双通道输出 (Read方法)
- 麦克风数据：右移8位保持精度（而非12位）
- 参考信号：从播放缓存中同步获取
- 交错输出：`[mic0, ref0, mic1, ref1, ...]`

### 3. AEC处理器配置
- AFE已配置为 `AEC_MODE_VOIP_HIGH_PERF` 高性能模式
- 支持16kHz采样率的AEC处理

## 测试步骤

### 1. 编译测试
```bash
cd xiaozhi-esp32-otto-5.2
idf.py set-target esp32s3
idf.py menuconfig  # 确认 USE_DEVICE_AEC 已启用
idf.py build
```

### 2. 功能测试
1. **基础音频**: 确认播放和录音功能正常
2. **实时对话**: 启用实时对话模式测试AEC效果
3. **打断测试**: 播放音频时说话，测试是否能正确识别

### 3. 效果验证
- **无AEC**: 播放时说话容易误识别播放内容
- **有AEC**: 播放时说话能正确识别语音内容
- **延迟**: 检查音频延迟是否在可接受范围内

## 关键技术点

### 1. 数据同步
- 播放和录音数据需要时间同步
- 当前实现：简单的循环缓存，±60ms同步误差可接受

### 2. 采样率统一
- 麦克风：16kHz (原有)
- 扬声器：16kHz (从24kHz修改)
- AEC处理：16kHz

### 3. 数据精度
- 麦克风数据：24bit → 16bit (右移8位)
- 参考信号：16bit直接使用
- 避免精度损失影响AEC效果

## 预期效果
- ✅ 支持语音打断播放
- ✅ 提高唤醒词识别准确率
- ✅ 实现实时对话模式
- ✅ 保持音频质量

## 代码修改总结

### 文件修改列表
1. `main/boards/otto-robot/config.h` - 采样率配置
2. `main/Kconfig.projbuild` - AEC支持配置
3. `main/audio_codecs/no_audio_codec.h` - 类声明和成员变量
4. `main/audio_codecs/no_audio_codec.cc` - 核心AEC实现

### 关键代码片段

#### 1. 配置启用 (config.h)
```cpp
#define AUDIO_OUTPUT_SAMPLE_RATE 16000  // 修改为16kHz以支持软件AEC
```

#### 2. 虚拟参考信号初始化
```cpp
#ifdef CONFIG_USE_DEVICE_AEC
    input_reference_ = true;        // 启用虚拟参考信号
    input_channels_ = 2;            // 双通道：麦克风+参考信号
    time_us_write_ = 0;
    time_us_read_ = 0;
    slice_index_ = 0;
#endif
```

#### 3. 播放缓存 (Write方法)
```cpp
#ifdef CONFIG_USE_DEVICE_AEC
    // 为软件AEC缓存播放数据
    {
        std::unique_lock<std::mutex> lock(mutex_);
        if (output_buffer_.size() < play_size*10) {
            output_buffer_.resize(play_size*10, 0);
            slice_index_ = 0;
        }

        for (int i = 0; i < samples; i++) {
            output_buffer_[slice_index_] = data[i];
            slice_index_++;
            if(slice_index_ >= play_size*10) slice_index_ = 0;
        }
    }
#endif
```

#### 4. 双通道读取 (Read方法)
```cpp
#ifdef CONFIG_USE_DEVICE_AEC
    if (input_reference_) {
        // 读取麦克风数据并生成参考信号
        for (int i = 0; i < samples; i++) {
            // 麦克风数据：右移8位保持精度
            int32_t value = bit32_buffer[i] >> 8;
            dest[i*2] = (int16_t)value;  // 麦克风通道

            // 参考信号：从播放缓存获取
            if (output_buffer_.size() > 0) {
                dest[i*2 + 1] = output_buffer_[i_index];
                i_index++;
            } else {
                dest[i*2 + 1] = 0;
            }
        }
        return samples*2;  // 返回双通道样本数
    }
#endif
```

## 编译修复

### 访问权限问题修复
原始代码中AEC相关成员变量被声明为`private`，导致派生类无法访问：

**修复前**:
```cpp
class NoAudioCodec : public AudioCodec {
private:
    // AEC成员变量在这里 - 派生类无法访问！
```

**修复后**:
```cpp
class NoAudioCodec : public AudioCodec {
private:
    virtual int Write(const int16_t* data, int samples) override;
    virtual int Read(int16_t* dest, int samples) override;

protected:  // 🔥 移动到protected区域
#ifdef CONFIG_USE_DEVICE_AEC
    std::vector<int16_t> output_buffer_;
    std::mutex mutex_;
    int64_t time_us_write_ = 0;
    int64_t time_us_read_ = 0;
    size_t slice_index_ = 0;
    size_t i_index = 0;
    static const size_t play_size = 1600;
#endif
```

### 线程安全改进
在Read方法中添加了线程安全保护和边界检查：

```cpp
// 参考信号通道：从播放缓存中获取
{
    std::unique_lock<std::mutex> lock(mutex_);
    if (output_buffer_.size() > 0 && i_index < output_buffer_.size()) {
        dest[i*2 + 1] = output_buffer_[i_index];
        i_index++;
        if(i_index >= play_size*10) i_index = 0;  // 循环缓存
    } else {
        dest[i*2 + 1] = 0;  // 没有播放数据时置零
    }
}
```

## 使用方法

### 1. 编译配置
otto-robot配置文件已自动启用：
```json
{
    "sdkconfig_append": [
        "CONFIG_USE_DEVICE_AEC=y"
    ]
}
```

### 2. 编译命令
```bash
cd xiaozhi-esp32-otto-5.2
idf.py set-target esp32s3
idf.py build
idf.py flash
```

### 3. 硬件要求
- ESP32S3芯片
- 16kHz采样率的麦克风和扬声器
- 足够的内存支持AEC处理

### 4. 测试验证
1. 播放音频时说话，检查是否能正确识别
2. 测试实时对话模式的打断功能
3. 验证音频质量和延迟

## 注意事项
- ✅ 编译错误已修复
- ✅ 线程安全保护已添加
- ✅ 边界检查已完善
- 需要ESP32S3足够的内存和计算能力
- AEC处理会增加一定的CPU负载
- 建议在实际硬件上测试效果
- 缓存大小为1.6秒，可根据需要调整
