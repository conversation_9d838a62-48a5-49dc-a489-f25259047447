# TTS音频数据流向详细分析

## 🎯 问题定位

**核心问题**: AI播放TTS时被自身音频误触发唤醒词检测，导致自我打断

**根本原因**: AFE/AEC没有正确接收扬声器的参考信号，无法区分AI输出和人声输入

## 📊 TTS音频数据完整流向

### 1. 服务器端数据发送

**数据格式**: Opus编码的音频包
**传输协议**: WebSocket (Binary Protocol 2/3)
**采样率**: 通常16kHz
**帧时长**: 60ms

### 2. 客户端接收处理

#### 2.1 协议层接收 (`main/protocols/websocket_protocol.cc`)

**关键代码位置**: 第116-149行
```cpp
websocket_->OnData([this](const char* data, size_t len, bool binary) {
    if (binary) {
        if (on_incoming_audio_ != nullptr) {
            if (version_ == 2) {
                BinaryProtocol2* bp2 = (BinaryProtocol2*)data;
                bp2->version = ntohs(bp2->version);
                bp2->type = ntohs(bp2->type);
                bp2->timestamp = ntohl(bp2->timestamp);
                bp2->payload_size = ntohl(bp2->payload_size);
                auto payload = (uint8_t*)bp2->payload;
                on_incoming_audio_(AudioStreamPacket{
                    .sample_rate = server_sample_rate_,      // 通常16kHz
                    .frame_duration = server_frame_duration_, // 通常60ms
                    .timestamp = bp2->timestamp,
                    .payload = std::vector<uint8_t>(payload, payload + bp2->payload_size)
                });
            }
            // ... 其他版本处理
        }
    }
});
```

**数据流向**: WebSocket二进制数据 → AudioStreamPacket → on_incoming_audio_回调

#### 2.2 应用层队列管理 (`main/application.cc`)

**关键代码位置**: 第478-483行
```cpp
protocol_->OnIncomingAudio([this](AudioStreamPacket&& packet) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (device_state_ == kDeviceStateSpeaking && audio_decode_queue_.size() < MAX_AUDIO_PACKETS_IN_QUEUE) {
        audio_decode_queue_.emplace_back(std::move(packet));  // 🔥 关键点1: 入队
    }
});
```

**数据结构**: `std::deque<AudioStreamPacket> audio_decode_queue_`
**队列限制**: MAX_AUDIO_PACKETS_IN_QUEUE (防止内存溢出)
**状态检查**: 只有在kDeviceStateSpeaking状态下才接收

#### 2.3 音频输出处理 (`main/application.cc`)

**关键代码位置**: 第799-853行
```cpp
void Application::OnAudioOutput() {
    // 1. 检查队列是否为空
    std::unique_lock<std::mutex> lock(mutex_);
    if (audio_decode_queue_.empty()) {
        return;
    }

    // 2. 取出音频包
    auto packet = std::move(audio_decode_queue_.front());  // 🔥 关键点2: 出队
    audio_decode_queue_.pop_front();
    lock.unlock();

    // 3. 后台任务处理
    background_task_->Schedule([this, codec, packet = std::move(packet)]() mutable {
        if (aborted_) {
            return;  // 打断检查
        }

        // 4. Opus解码
        std::vector<int16_t> pcm;
        if (!opus_decoder_->Decode(std::move(packet.payload), pcm)) {  // 🔥 关键点3: 解码
            return;
        }

        // 5. 重采样 (16kHz → 24kHz)
        if (opus_decoder_->sample_rate() != codec->output_sample_rate()) {
            int target_size = output_resampler_.GetOutputSamples(pcm.size());
            std::vector<int16_t> resampled(target_size);
            output_resampler_.Process(pcm.data(), pcm.size(), resampled.data());  // 🔥 关键点4: 重采样
            pcm = std::move(resampled);
        }

        // 6. 输出到扬声器
        codec->OutputData(pcm);  // 🔥 关键点5: 最终输出
    });
}
```

#### 2.4 音频编解码器输出 (`main/audio_codecs/audio_codec.cc`)

**关键代码位置**: 第17-19行
```cpp
void AudioCodec::OutputData(std::vector<int16_t>& data) {
    Write(data.data(), data.size());  // 调用具体编解码器的Write方法
}
```

**实际实现**: 对于Otto机器人使用`NoAudioCodecSimplex`
**最终输出**: I2S接口 → MAX98357A功放 → 扬声器

### 3. 关键数据转换点

#### 3.1 数据格式转换链
```
服务器Opus包 → AudioStreamPacket.payload (std::vector<uint8_t>)
                ↓ opus_decoder_->Decode()
              
                ↓ output_resampler_.Process()
               重采样PCM (std::vector<int16_t>, 24kHz)
                ↓ codec->OutputData()
               I2S输出 → MAX98357A → 扬声器
```

#### 3.2 采样率转换
- **输入**: 16kHz (服务器TTS)
- **输出**: 24kHz (扬声器配置)
- **转换器**: `output_resampler_` (Resampler类)

#### 3.3 音频时序
- **帧时长**: 60ms
- **缓冲队列**: audio_decode_queue_ (动态长度)
- **处理延迟**: 后台任务异步处理

## 🎯 AEC参考信号缺失分析

### 当前问题

1. **AFE配置问题**:
   - AFE只接收麦克风输入
   - 没有扬声器参考信号输入
   - AEC无法区分回声和真实语音

2. **数据流隔离**:
   - TTS播放路径: 服务器 → 解码 → 扬声器
   - 麦克风路径: 麦克风 → AFE → 唤醒词检测
   - **两个路径完全独立，没有交集**

3. **关键缺失点**:
   - 在`codec->OutputData(pcm)`之后，PCM数据没有同时送入AFE作为参考信号
   - AFE配置中的参考通道没有被正确使用

### 解决方案要点

**需要在以下位置添加参考信号**:
```cpp
// 在 Application::OnAudioOutput() 中
codec->OutputData(pcm);  // 现有输出

// 🔥 需要添加: 同时将PCM数据作为参考信号送入AFE
if (wake_word_->IsDetectionRunning() || audio_processor_->IsRunning()) {
    FeedReferenceSignal(pcm);  // 新增方法
}
```

**关键实现点**:
1. 将24kHz的PCM数据重采样回16kHz
2. 送入AFE作为参考信号
3. 确保时序同步
```

<function_calls>
<invoke name="view">
<parameter name="path">main/protocols/websocket_protocol.cc
