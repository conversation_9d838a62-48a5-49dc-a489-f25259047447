#include "dual_mic_audio_codec.h"
#include <esp_log.h>

#define TAG "DualMicAudioCodec"

DualMicAudioCodec::DualMicAudioCodec(int input_sample_rate, int output_sample_rate,
                                     gpio_num_t mclk, gpio_num_t bclk, gpio_num_t ws,
                                     gpio_num_t dout, gpio_num_t din0, gpio_num_t din1)
    : mclk_pin_(mclk), bclk_pin_(bclk), ws_pin_(ws),
      dout_pin_(dout), din0_pin_(din0), din1_pin_(din1) {
    
    duplex_ = true;
    input_reference_ = true; // 启用参考麦克风
    input_channels_ = 2;     // 双通道输入
    input_sample_rate_ = input_sample_rate;
    output_sample_rate_ = output_sample_rate;
    output_channels_ = 1;    // 单声道输出
    
    ConfigureI2S();
}

DualMicAudioCodec::~DualMicAudioCodec() {
    if (tx_handle_) i2s_del_channel(tx_handle_);
    if (rx_handle_) i2s_del_channel(rx_handle_);
}

void DualMicAudioCodec::ConfigureI2S() {
    // 配置I2S通道
    i2s_chan_config_t chan_cfg = I2S_CHANNEL_DEFAULT_CONFIG(I2S_NUM_0, I2S_ROLE_MASTER);
    chan_cfg.auto_clear = true;
    ESP_ERROR_CHECK(i2s_new_channel(&chan_cfg, &tx_handle_, &rx_handle_));
    
    // 配置发送端（扬声器输出）
    i2s_std_config_t std_tx_cfg = {
        .clk_cfg = I2S_STD_CLK_DEFAULT_CONFIG(output_sample_rate_),
        .slot_cfg = I2S_STD_PHILIPS_SLOT_DEFAULT_CONFIG(I2S_DATA_BIT_WIDTH_16BIT, I2S_SLOT_MODE_MONO),
        .gpio_cfg = {
            .mclk = mclk_pin_,
            .bclk = bclk_pin_,
            .ws = ws_pin_,
            .dout = dout_pin_,
            .din = I2S_GPIO_UNUSED,
            .invert_flags = {
                .mclk_inv = false,
                .bclk_inv = false,
                .ws_inv = false,
            },
        },
    };
    ESP_ERROR_CHECK(i2s_channel_init_std_mode(tx_handle_, &std_tx_cfg));
    
    // 配置接收端（双麦克风输入）
    i2s_std_config_t std_rx_cfg = {
        .clk_cfg = I2S_STD_CLK_DEFAULT_CONFIG(input_sample_rate_),
        .slot_cfg = I2S_STD_MSB_SLOT_DEFAULT_CONFIG(I2S_DATA_BIT_WIDTH_32BIT, I2S_SLOT_MODE_STEREO),
        .gpio_cfg = {
            .mclk = mclk_pin_,
            .bclk = bclk_pin_,
            .ws = ws_pin_,
            .dout = I2S_GPIO_UNUSED,
            .din = din0_pin_, // 主麦克风使用DIN0
            .invert_flags = {
                .mclk_inv = false,
                .bclk_inv = false,
                .ws_inv = false,
            },
        },
    };
    // 启用第二个数据线用于参考麦克风
    std_rx_cfg.gpio_cfg.din2 = din1_pin_;
    ESP_ERROR_CHECK(i2s_channel_init_std_mode(rx_handle_, &std_rx_cfg));
    
    ESP_LOGI(TAG, "I2S configured for dual-mic AEC");
}

void DualMicAudioCodec::Start() {
    AudioCodec::Start();
    ESP_ERROR_CHECK(i2s_channel_enable(tx_handle_));
    ESP_ERROR_CHECK(i2s_channel_enable(rx_handle_));
    ESP_LOGI(TAG, "Dual-mic audio codec started");
}

int DualMicAudioCodec::Read(int16_t* dest, int samples) {
    size_t bytes_read = 0;
    int32_t* buffer = new int32_t[samples * 2]; // 32位立体声数据
    
    // 读取原始32位立体声数据
    i2s_channel_read(rx_handle_, buffer, samples * 8, &bytes_read, portMAX_DELAY);
    
    // 转换为16位并分离通道
    for (int i = 0; i < samples; i++) {
        // 主麦克风（左声道）
        dest[i * 2] = static_cast<int16_t>(buffer[i * 2] >> 16);
        // 参考麦克风（右声道）
        dest[i * 2 + 1] = static_cast<int16_t>(buffer[i * 2 + 1] >> 16);
    }
    
    delete[] buffer;
    return samples;
}

int DualMicAudioCodec::Write(const int16_t* data, int samples) {
    size_t bytes_written = 0;
    // 单声道转立体声输出
    int32_t* buffer = new int32_t[samples * 2];
    
    for (int i = 0; i < samples; i++) {
        buffer[i * 2] = static_cast<int32_t>(data[i]) << 16; // 左声道
        buffer[i * 2 + 1] = static_cast<int32_t>(data[i]) << 16; // 右声道
    }
    
    i2s_channel_write(tx_handle_, buffer, samples * 8, &bytes_written, portMAX_DELAY);
    delete[] buffer;
    return samples;
}

// 以下方法保持默认实现
void DualMicAudioCodec::SetOutputVolume(int volume) {
    output_volume_ = std::max(0, std::min(100, volume));
    ESP_LOGI(TAG, "Output volume set to %d", output_volume_);
}

void DualMicAudioCodec::EnableInput(bool enable) {
    input_enabled_ = enable;
    ESP_LOGI(TAG, "Input %s", enable ? "enabled" : "disabled");
}

void DualMicAudioCodec::EnableOutput(bool enable) {
    output_enabled_ = enable;
    ESP_LOGI(TAG, "Output %s", enable ? "enabled" : "disabled");
}
