# Otto机器人四肢功能屏蔽说明

## 概述
本文档说明了Otto机器人代码中四肢功能的完全屏蔽实现，确保只保留头部功能（表情显示、音频、摄像头等）。

## 屏蔽的功能
### 四肢动作功能
- 行走 (Walk)
- 转身 (Turn) 
- 跳跃 (Jump)
- 摇摆 (Swing)
- 太空步 (Moonwalk)
- 弯曲 (Bend)
- 摇腿 (ShakeLeg)
- 上下运动 (UpDown)
- 脚尖摇摆 (TiptoeSwing)
- 抖动 (Jitter)
- 上升转身 (AscendingTurn)
- 十字步 (Crusaito)
- 拍翅膀 (Flapping)

### 手部动作功能
- 举手 (HandsUp)
- 放手 (HandsDown)
- 挥手 (HandWave)
- 双手挥手 (HandWaveBoth)

### 舵机相关功能
- 舵机初始化和连接
- 舵机微调设置
- 舵机限速功能
- HOME位置复位

## 保留的头部功能
### 表情显示
- GIF动画表情 (happy, sad, angry, surprised, etc.)
- 静态表情图标
- 表情映射和切换

### 音频功能
- 麦克风输入 (GPIO 4, 5, 6)
- 扬声器输出 (GPIO 7, 15, 16)
- 音频编解码
- 语音识别和合成

### 显示功能
- LCD显示屏 (240x240, ST7789)
- 聊天消息显示
- 状态指示
- 背光控制

### 摄像头功能
- 图像捕获
- 预览显示
- 镜像控制

### 系统功能
- WiFi连接
- 电池电量监测
- 按键控制
- LED状态指示
- OTA升级

## 技术实现

### 1. 配置文件屏蔽 (config.h)
```c
#define ENABLE_LIMB_ACTIONS 0  // 0=不编译四肢动作相关代码，1=保留

// 所有四肢引脚设置为-1（禁用）
#define RIGHT_LEG_PIN  -1
#define RIGHT_FOOT_PIN -1  
#define LEFT_LEG_PIN   -1
#define LEFT_FOOT_PIN  -1
#define LEFT_HAND_PIN  -1
#define RIGHT_HAND_PIN -1
```

### 2. 条件编译屏蔽
- `otto_movements.cc`: 整个文件使用 `#if ENABLE_LIMB_ACTIONS` 包围
- `otto_movements.h`: 提供空实现类当四肢功能禁用时
- `otto_controller.cc`: 关键功能使用条件编译

### 3. 运行时检查
- 动作队列中检查 `ENABLE_LIMB_ACTIONS` 标志
- MCP工具注册时跳过四肢相关工具
- 初始化时跳过舵机连接和微调加载

### 4. MCP工具屏蔽
以下MCP工具在四肢功能屏蔽时不会注册：
- `self.otto.walk_forward`
- `self.otto.turn_left` 
- `self.otto.jump`
- `self.otto.swing`
- `self.otto.moonwalk`
- `self.otto.bend`
- `self.otto.shake_leg`
- `self.otto.updown`
- `self.otto.hands_up`
- `self.otto.hands_down`
- `self.otto.set_trim`
- `self.otto.get_trims`

保留的系统工具：
- `self.otto.stop` (立即停止)
- `self.otto.get_status` (获取状态)
- `self.battery.get_level` (电池电量)

## 引脚分配

### 头部功能引脚 (保留)
```c
// 音频
#define AUDIO_I2S_MIC_GPIO_WS GPIO_NUM_4
#define AUDIO_I2S_MIC_GPIO_SCK GPIO_NUM_5  
#define AUDIO_I2S_MIC_GPIO_DIN GPIO_NUM_6
#define AUDIO_I2S_SPK_GPIO_DOUT GPIO_NUM_7
#define AUDIO_I2S_SPK_GPIO_BCLK GPIO_NUM_15
#define AUDIO_I2S_SPK_GPIO_LRCK GPIO_NUM_16

// 显示
#define DISPLAY_BACKLIGHT_PIN GPIO_NUM_3
#define DISPLAY_MOSI_PIN GPIO_NUM_10
#define DISPLAY_CLK_PIN GPIO_NUM_9
#define DISPLAY_DC_PIN GPIO_NUM_46
#define DISPLAY_RST_PIN GPIO_NUM_11
#define DISPLAY_CS_PIN GPIO_NUM_12

// 系统
#define BOOT_BUTTON_GPIO GPIO_NUM_0
#define POWER_CHARGE_DETECT_PIN GPIO_NUM_21
```

### 四肢功能引脚 (已禁用)
```c
#define RIGHT_LEG_PIN  -1  // 已禁用
#define RIGHT_FOOT_PIN -1  // 已禁用  
#define LEFT_LEG_PIN   -1  // 已禁用
#define LEFT_FOOT_PIN  -1  // 已禁用
#define LEFT_HAND_PIN  -1  // 已禁用
#define RIGHT_HAND_PIN -1  // 已禁用
```

## 日志输出
当四肢功能屏蔽时，系统会输出以下日志信息：
- "Otto机器人初始化（四肢功能已屏蔽，仅保留头部功能）"
- "四肢功能已屏蔽，跳过加载微调设置"
- "跳过HOME动作（四肢功能已屏蔽）"
- "四肢动作已屏蔽，忽略动作类型: X"

## 编译和部署
1. 确保 `config.h` 中 `ENABLE_LIMB_ACTIONS` 设置为 `0`
2. 重新编译项目
3. 烧录到ESP32设备
4. 验证只有头部功能可用

## 恢复四肢功能
如需恢复四肢功能：
1. 修改 `config.h` 中 `ENABLE_LIMB_ACTIONS` 为 `1`
2. 配置正确的舵机引脚号
3. 重新编译和烧录

## 注意事项
- 屏蔽四肢功能后，所有与舵机相关的硬件连接都不会被初始化
- 头部功能完全独立，不受四肢功能屏蔽影响
- 系统仍保留完整的AI对话、表情显示、音频处理等核心功能
- 电池管理和WiFi连接等系统功能正常工作
